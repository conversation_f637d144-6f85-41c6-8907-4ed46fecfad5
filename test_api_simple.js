// Простой тест API endpoints
const axios = require('axios');

const BASE_URL = 'http://localhost:4000';

async function testAPI() {
  console.log('🧪 Тестирование API после исправления БД...\n');

  try {
    // Тест 1: Получение донатов
    console.log('1. Тестируем получение донатов...');
    const donationsResponse = await axios.get(`${BASE_URL}/donations`);
    console.log('✅ /donations работает:', donationsResponse.data);

    // Тест 2: Получение устройств тестового стримера
    console.log('\n2. Тестируем получение устройств по нику...');
    const devicesResponse = await axios.get(`${BASE_URL}/devices-by-nickname/teststreamer`);
    console.log('✅ /devices-by-nickname работает:', devicesResponse.data);

    // Тест 3: Получение настроек режима управления
    console.log('\n3. Тестируем получение настроек режима...');
    const settingsResponse = await axios.get(`${BASE_URL}/control-mode-settings/EQTestStreamer123`);
    console.log('✅ /control-mode-settings работает:', settingsResponse.data);

    // Тест 4: Получение ника стримера
    console.log('\n4. Тестируем получение ника стримера...');
    const nicknameResponse = await axios.get(`${BASE_URL}/get-nickname/EQTestStreamer123`);
    console.log('✅ /get-nickname работает:', nicknameResponse.data);

    // Тест 5: Получение информации о режиме управления стримера
    console.log('\n5. Тестируем получение режима управления стримера...');
    const controlModeResponse = await axios.get(`${BASE_URL}/streamer-control-mode/teststreamer`);
    console.log('✅ /streamer-control-mode работает:', controlModeResponse.data);

    // Тест 6: Авторизация
    console.log('\n6. Тестируем авторизацию...');
    const authResponse = await axios.post(`${BASE_URL}/auth`, {
      user: 'EQTestUser123'
    });
    console.log('✅ /auth работает:', authResponse.data);

    console.log('\n🎉 Все тесты прошли успешно! База данных работает корректно.');

  } catch (error) {
    console.error('\n❌ Ошибка при тестировании:', error.response?.data || error.message);
  }
}

testAPI();
