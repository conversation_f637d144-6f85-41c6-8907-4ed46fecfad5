// Скрипт для исправления проблем с базой данных
require("dotenv").config();
const { Pool } = require('pg');

// Создаем подключение к базе данных
const pool = new Pool({
  user: process.env.DB_USER || "postgres",
  host: process.env.DB_HOST || "***********",
  database: process.env.DB_NAME || "esp32_db",
  password: process.env.DB_PASSWORD || "Fast777",
  port: process.env.DB_PORT || 5432,
  ssl: false,
  connectionTimeoutMillis: 10000, // 10 секунд таймаут
});

async function checkAndFixDatabase() {
  console.log('🔧 Проверка и исправление базы данных...\n');
  
  let client;
  try {
    // Подключаемся к базе данных
    client = await pool.connect();
    console.log('✅ Подключение к базе данных успешно!');
    
    // Проверяем версию PostgreSQL
    const versionResult = await client.query('SELECT version()');
    console.log('📊 Версия PostgreSQL:', versionResult.rows[0].version.split(' ')[0] + ' ' + versionResult.rows[0].version.split(' ')[1]);
    
    // Проверяем существующие таблицы
    console.log('\n📋 Проверка существующих таблиц...');
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);
    
    const existingTables = tablesResult.rows.map(row => row.table_name);
    console.log('Существующие таблицы:', existingTables.length > 0 ? existingTables : 'Нет таблиц');
    
    // Список необходимых таблиц
    const requiredTables = ['streamers', 'devices', 'lot', 'control_settings', 'sponsor'];
    const missingTables = requiredTables.filter(table => !existingTables.includes(table));
    
    if (missingTables.length > 0) {
      console.log('\n❌ Отсутствующие таблицы:', missingTables);
      console.log('\n🔨 Создание отсутствующих таблиц...');
      
      // Создаем таблицы по одной
      if (missingTables.includes('streamers')) {
        console.log('Создание таблицы streamers...');
        await client.query(`
          CREATE TABLE streamers (
            id SERIAL PRIMARY KEY,
            wallet_address TEXT NOT NULL UNIQUE,
            nickname TEXT UNIQUE,
            stream_url TEXT,
            stream_platform TEXT,
            stream_id TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          )
        `);
        console.log('✅ Таблица streamers создана');
      }
      
      if (missingTables.includes('devices')) {
        console.log('Создание таблицы devices...');
        await client.query(`
          CREATE TABLE devices (
            id SERIAL PRIMARY KEY,
            device_id TEXT,
            wallet_address TEXT NOT NULL,
            name TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          )
        `);
        console.log('✅ Таблица devices создана');
      }
      
      if (missingTables.includes('lot')) {
        console.log('Создание таблицы lot...');
        await client.query(`
          CREATE TABLE lot (
            device_id TEXT PRIMARY KEY,
            device_type TEXT,
            is_online BOOLEAN DEFAULT FALSE,
            connected_at TIMESTAMP,
            disconnected_at TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          )
        `);
        console.log('✅ Таблица lot создана');
      }
      
      if (missingTables.includes('control_settings')) {
        console.log('Создание таблицы control_settings...');
        await client.query(`
          CREATE TABLE control_settings (
            id SERIAL PRIMARY KEY,
            wallet_address TEXT NOT NULL UNIQUE,
            mode TEXT NOT NULL DEFAULT 'donation-battle',
            time_minutes INTEGER DEFAULT 5,
            price_ton NUMERIC(10, 2) DEFAULT 1.0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          )
        `);
        console.log('✅ Таблица control_settings создана');
      }
      
      if (missingTables.includes('sponsor')) {
        console.log('Создание таблицы sponsor...');
        await client.query(`
          CREATE TABLE sponsor (
            id SERIAL PRIMARY KEY,
            wallet_address TEXT NOT NULL UNIQUE,
            nickname TEXT NOT NULL UNIQUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          )
        `);
        console.log('✅ Таблица sponsor создана');
      }
      
      // Создаем индексы
      console.log('\n📊 Создание индексов...');
      await client.query('CREATE INDEX IF NOT EXISTS idx_streamers_wallet_address ON streamers(wallet_address)');
      await client.query('CREATE INDEX IF NOT EXISTS idx_streamers_nickname ON streamers(nickname)');
      await client.query('CREATE INDEX IF NOT EXISTS idx_devices_wallet_address ON devices(wallet_address)');
      await client.query('CREATE INDEX IF NOT EXISTS idx_control_settings_wallet_address ON control_settings(wallet_address)');
      console.log('✅ Индексы созданы');
      
      // Добавляем тестовые данные
      console.log('\n📝 Добавление тестовых данных...');
      
      // Тестовый стример
      await client.query(`
        INSERT INTO streamers (wallet_address, nickname, stream_url, stream_platform, stream_id) 
        VALUES ('EQTestStreamer123', 'teststreamer', 'https://www.youtube.com/live/test123', 'youtube', 'test123')
        ON CONFLICT (wallet_address) DO NOTHING
      `);
      
      // Настройки управления для тестового стримера
      await client.query(`
        INSERT INTO control_settings (wallet_address, mode, time_minutes, price_ton)
        VALUES ('EQTestStreamer123', 'donation-battle', 5, 1.0)
        ON CONFLICT (wallet_address) DO NOTHING
      `);
      
      // Тестовый спонсор
      await client.query(`
        INSERT INTO sponsor (wallet_address, nickname)
        VALUES ('EQSponsor123', 'testsponsor')
        ON CONFLICT (wallet_address) DO NOTHING
      `);
      
      // Добавляем подключенное устройство
      await client.query(`
        INSERT INTO lot (device_id, device_type, is_online, connected_at)
        VALUES ('AC:67:B2:10:2F:2C', 'motor', TRUE, NOW())
        ON CONFLICT (device_id) DO UPDATE SET 
          device_type = EXCLUDED.device_type,
          is_online = EXCLUDED.is_online,
          connected_at = EXCLUDED.connected_at
      `);
      
      // Привязываем устройство к тестовому стримеру
      await client.query(`
        INSERT INTO devices (device_id, wallet_address, name)
        VALUES ('AC:67:B2:10:2F:2C', 'EQTestStreamer123', 'Тестовый мотор')
        ON CONFLICT DO NOTHING
      `);
      
      console.log('✅ Тестовые данные добавлены');
      
    } else {
      console.log('\n✅ Все необходимые таблицы существуют');
    }
    
    // Проверяем финальное состояние
    console.log('\n📊 Финальная проверка таблиц...');
    const finalTablesResult = await client.query(`
      SELECT table_name, 
             (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count
      FROM information_schema.tables t
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);
    
    finalTablesResult.rows.forEach(row => {
      console.log(`  ✅ ${row.table_name} (${row.column_count} колонок)`);
    });
    
    console.log('\n🎉 База данных готова к работе!');
    
  } catch (error) {
    console.error('\n❌ Ошибка при работе с базой данных:');
    console.error('Код ошибки:', error.code);
    console.error('Сообщение:', error.message);
    
    if (error.code === 'ENOTFOUND') {
      console.error('🔍 Хост не найден. Проверьте правильность адреса.');
    } else if (error.code === 'ECONNREFUSED') {
      console.error('🚫 Соединение отклонено. Проверьте, что PostgreSQL запущен и доступен.');
    } else if (error.code === 'ETIMEDOUT') {
      console.error('⏰ Таймаут подключения. Проверьте сетевое соединение.');
    } else if (error.code === '28P01') {
      console.error('🔐 Ошибка аутентификации. Проверьте логин и пароль.');
    } else if (error.code === '3D000') {
      console.error('🗄️ База данных не существует.');
    }
  } finally {
    if (client) {
      client.release();
    }
    await pool.end();
  }
}

checkAndFixDatabase();
